{"name": "plc-management-frontend", "version": "1.0.0", "description": "PLC Auto Coding System - Frontend Management Interface", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:host": "vite --host 0.0.0.0", "build": "vite build", "build:check": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "@monaco-editor/loader": "^1.4.0", "monaco-editor": "^0.45.0", "dayjs": "^1.11.10"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "vue-tsc": "^2.0.0", "typescript": "^5.3.2", "@types/node": "^20.10.0", "unplugin-vue-components": "^0.26.0", "unplugin-auto-import": "^0.17.2", "sass": "^1.69.5"}}