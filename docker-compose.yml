version: '3.8'

services:
  # 后端API服务
  backend:
    image: *************:5000/plc-auto-coding-backend:${PLC_BACKEND_TAG:-latest}
    environment:
      - DATABASE_HOST=*************
      - DATABASE_PORT=3306
      - DATABASE_USER=root
      - DATABASE_PASSWORD=leadchina
      - DATABASE_NAME=IO_TEST
      - DEBUG=false
    networks:
      - plc-network
    restart: unless-stopped

  # Nginx反向代理和前端静态文件服务
  nginx:
    image: *************:5000/plc-auto-coding-frontend:${PLC_FRONTEND_TAG:-latest}
    ports:
      - "30091:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend
    networks:
      - plc-network
    restart: unless-stopped

networks:
  plc-network:
    driver: bridge
