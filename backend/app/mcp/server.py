"""
MCP Server 实现
提供基于FastMCP的MCP服务，用于查询和代码生成
"""

import json
import asyncio
from typing import Any, Dict, List, Optional, AsyncGenerator
from datetime import datetime
from pydantic import BaseModel, Field

import mcp
from mcp import Context

from sqlalchemy.orm import Session
from ..api.deps import get_database
from ..services.code_generator import code_generator_service
from ..crud.tp_control import tp_control
from ..crud.op_control import op_control
from ..crud.actuator_map import actuator_map


# 定义输入输出模型
class QueryTPControlInput(BaseModel):
    step_id: Optional[int] = Field(None, description="步骤ID筛选")
    em_description: Optional[str] = Field(None, description="EM描述筛选")
    em_variable: Optional[str] = Field(None, description="EM变量筛选")
    limit: int = Field(50, description="返回记录数限制")


class QueryOPControlInput(BaseModel):
    step_id: Optional[int] = Field(None, description="步骤ID筛选")
    em_description: Optional[str] = Field(None, description="EM描述筛选")
    em_variable: Optional[str] = Field(None, description="EM变量筛选")
    limit: int = Field(50, description="返回记录数限制")


class QueryActuatorMapInput(BaseModel):
    command_description: Optional[str] = Field(None, description="命令描述筛选")
    variable_name: Optional[str] = Field(None, description="变量名筛选")
    limit: int = Field(50, description="返回记录数限制")


class GenerateCodeInput(BaseModel):
    em_description: Optional[str] = Field(None, description="EM描述筛选，用于生成特定EM的代码")


class ValidateConditionsInput(BaseModel):
    detailed: bool = Field(False, description="是否返回详细分析")


class TestConditionMappingInput(BaseModel):
    condition_text: str = Field(..., description="要测试的条件文本")
    step_data: Optional[Dict[str, str]] = Field(
        default_factory=lambda: {
            "em_variable": "io_stEmBtryIn",
            "op_sequence_under_em": "OP[1]"
        },
        description="步骤数据上下文"
    )


class PLCQueryResult(BaseModel):
    success: bool
    total: int
    items: List[Dict[str, Any]]
    message: Optional[str] = None
    error: Optional[str] = None


class CodeGenerationResult(BaseModel):
    success: bool
    code: Optional[str] = None
    warnings: List[str] = Field(default_factory=list)
    unmapped_conditions: List[str] = Field(default_factory=list)
    generated_at: str
    error: Optional[str] = None


class ValidationResult(BaseModel):
    success: bool
    validation_data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


# MCP工具函数
@mcp.tool()
async def query_tp_control(ctx: Context, inputs: QueryTPControlInput) -> PLCQueryResult:
    """
    查询TP控制逻辑数据

    Args:
        ctx: FastMCP上下文对象
        inputs: 查询参数，包含step_id、em_description、em_variable、limit

    Returns:
        PLCQueryResult: 查询结果，包含总数、数据项列表等
    """
    await ctx.info(f"查询TP控制逻辑: {inputs.dict()}")

    try:
        db = next(get_database())

        filters = {}
        if inputs.step_id is not None:
            filters["step_id"] = str(inputs.step_id)
        if inputs.em_description:
            filters["em_description_cn"] = inputs.em_description
        if inputs.em_variable:
            filters["em_variable"] = inputs.em_variable

        items = tp_control.get_multi(db, skip=0, limit=inputs.limit, filters=filters)
        total = tp_control.count(db, filters=filters)

        result_items = [
            {
                "id": item.id,
                "step_id": item.step_id,
                "step_description": item.step_description,
                "motion_condition": item.motion_condition,
                "motion_action": item.motion_action,
                "skip_condition": item.skip_condition,
                "skip_step": item.skip_step,
                "tp_sequence": item.tp_sequence,
                "em_description_cn": item.em_description_cn,
                "em_variable": item.em_variable,
                "op_sequence_under_em": item.op_sequence_under_em
            }
            for item in items
        ]

        await ctx.info(f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录")

        return PLCQueryResult(
            success=True,
            total=total,
            items=result_items,
            message=f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录"
        )

    except Exception as e:
        await ctx.error(f"查询TP控制逻辑失败: {e}")
        return PLCQueryResult(
            success=False,
            total=0,
            items=[],
            error=str(e)
        )
    finally:
        db.close()


@mcp.tool()
async def query_op_control(ctx: Context, inputs: QueryOPControlInput) -> PLCQueryResult:
    """
    查询OP控制逻辑数据

    Args:
        ctx: FastMCP上下文对象
        inputs: 查询参数，包含step_id、em_description、em_variable、limit

    Returns:
        PLCQueryResult: 查询结果，包含总数、数据项列表等
    """
    await ctx.info(f"查询OP控制逻辑: {inputs.dict()}")

    try:
        db = next(get_database())

        filters = {}
        if inputs.step_id is not None:
            filters["step_id"] = str(inputs.step_id)
        if inputs.em_description:
            filters["em_description_cn"] = inputs.em_description
        if inputs.em_variable:
            filters["em_variable"] = inputs.em_variable

        items = op_control.get_multi(db, skip=0, limit=inputs.limit, filters=filters)
        total = op_control.count(db, filters=filters)

        result_items = [
            {
                "id": item.id,
                "step_id": item.step_id,
                "step_description": item.step_description,
                "command_description_cn": item.command_description_cn,
                "recovery_action_description_cn": item.recovery_action_description_cn,
                "em_description_cn": item.em_description_cn,
                "em_variable": item.em_variable,
                "op_sequence_under_em": item.op_sequence_under_em
            }
            for item in items
        ]

        await ctx.info(f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录")

        return PLCQueryResult(
            success=True,
            total=total,
            items=result_items,
            message=f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录"
        )

    except Exception as e:
        await ctx.error(f"查询OP控制逻辑失败: {e}")
        return PLCQueryResult(
            success=False,
            total=0,
            items=[],
            error=str(e)
        )
    finally:
        db.close()


@mcp.tool()
async def query_actuator_map(ctx: Context, inputs: QueryActuatorMapInput) -> PLCQueryResult:
    """
    查询执行器变量映射数据

    Args:
        ctx: FastMCP上下文对象
        inputs: 查询参数，包含command_description、variable_name、limit

    Returns:
        PLCQueryResult: 查询结果，包含总数、数据项列表等
    """
    await ctx.info(f"查询执行器变量映射: {inputs.model_dump()}")

    try:
        db = next(get_database())

        filters = {}
        if inputs.command_description:
            filters["command_description_cn"] = inputs.command_description
        if inputs.variable_name:
            filters["variable_name"] = inputs.variable_name

        items = actuator_map.get_multi(db, skip=0, limit=inputs.limit, filters=filters)
        total = actuator_map.count(db, filters=filters)

        result_items = [
            {
                "id": item.id,
                "command_description_cn": item.command_description_cn,
                "variable_name": item.variable_name,
                "parent_struct": item.parent_struct,
                "actuator": item.actuator,
                "op": item.op
            }
            for item in items
        ]

        await ctx.info(f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录")

        return PLCQueryResult(
            success=True,
            total=total,
            items=result_items,
            message=f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录"
        )

    except Exception as e:
        await ctx.error(f"查询执行器变量映射失败: {e}")
        return PLCQueryResult(
            success=False,
            total=0,
            items=[],
            error=str(e)
        )
    finally:
        db.close()


@mcp.tool()
async def generate_tp_code(ctx: Context, inputs: GenerateCodeInput) -> CodeGenerationResult:
    """
    生成TP代码

    Args:
        ctx: FastMCP上下文对象
        inputs: 代码生成参数，包含em_description

    Returns:
        CodeGenerationResult: 代码生成结果，包含生成的代码、警告、未映射条件等
    """
    await ctx.info(f"生成TP代码: {inputs.model_dump()}")

    try:
        db = next(get_database())

        code, warnings, unmapped = code_generator_service.generate_tp_code(
            db, em_description=inputs.em_description
        )

        await ctx.info(f"TP代码生成完成: 警告 {len(warnings)} 个, 未映射条件 {len(unmapped)} 个")

        return CodeGenerationResult(
            success=True,
            code=code,
            warnings=warnings,
            unmapped_conditions=unmapped,
            generated_at=datetime.now().isoformat()
        )

    except Exception as e:
        await ctx.error(f"TP代码生成失败: {e}")
        return CodeGenerationResult(
            success=False,
            generated_at=datetime.now().isoformat(),
            error=str(e)
        )
    finally:
        db.close()


@mcp.tool()
async def generate_op_code(ctx: Context, inputs: GenerateCodeInput) -> CodeGenerationResult:
    """
    生成OP代码

    Args:
        ctx: FastMCP上下文对象
        inputs: 代码生成参数，包含em_description

    Returns:
        CodeGenerationResult: 代码生成结果，包含生成的代码、警告、未映射条件等
    """
    await ctx.info(f"生成OP代码: {inputs.model_dump()}")

    try:
        db = next(get_database())

        code, warnings, unmapped = code_generator_service.generate_op_code(
            db, em_description=inputs.em_description
        )

        await ctx.info(f"OP代码生成完成: 警告 {len(warnings)} 个, 未映射条件 {len(unmapped)} 个")

        return CodeGenerationResult(
            success=True,
            code=code,
            warnings=warnings,
            unmapped_conditions=unmapped,
            generated_at=datetime.now().isoformat()
        )

    except Exception as e:
        await ctx.error(f"OP代码生成失败: {e}")
        return CodeGenerationResult(
            success=False,
            generated_at=datetime.now().isoformat(),
            error=str(e)
        )
    finally:
        db.close()


@mcp.tool()
async def validate_conditions(ctx: Context, inputs: ValidateConditionsInput) -> ValidationResult:
    """
    验证条件映射完整性

    Args:
        ctx: FastMCP上下文对象
        inputs: 验证参数，包含detailed

    Returns:
        ValidationResult: 验证结果，包含验证数据
    """
    await ctx.info(f"验证条件映射: {inputs.model_dump()}")

    try:
        db = next(get_database())

        validation_result = code_generator_service.validate_conditions(db)

        if inputs.detailed:
            detailed_analysis = code_generator_service.analyze_conditions_detailed(db)
            validation_result.update(detailed_analysis)

        await ctx.info("条件映射验证完成")

        return ValidationResult(
            success=True,
            validation_data=validation_result
        )

    except Exception as e:
        await ctx.error(f"条件映射验证失败: {e}")
        return ValidationResult(
            success=False,
            error=str(e)
        )
    finally:
        db.close()


@mcp.tool()
async def test_condition_mapping(ctx: Context, inputs: TestConditionMappingInput) -> ValidationResult:
    """
    测试单个条件的映射结果

    Args:
        ctx: FastMCP上下文对象
        inputs: 测试参数，包含condition_text和step_data

    Returns:
        ValidationResult: 测试结果，包含映射结果和建议
    """
    await ctx.info(f"测试条件映射: {inputs.model_dump()}")

    try:
        db = next(get_database())

        # 测试条件映射
        mapping_result = code_generator_service.find_condition_mapping(
            db, inputs.condition_text, inputs.step_data
        )

        # 获取映射建议
        suggestions = code_generator_service.get_condition_mapping_suggestions(
            db, inputs.condition_text
        )

        result_data = {
            "condition": inputs.condition_text,
            "step_data": inputs.step_data,
            "mapped_result": mapping_result,
            "suggestions": suggestions
        }

        await ctx.info("条件映射测试完成")

        return ValidationResult(
            success=True,
            validation_data=result_data
        )

    except Exception as e:
        await ctx.error(f"条件映射测试失败: {e}")
        return ValidationResult(
            success=False,
            error=str(e)
        )
    finally:
        db.close()


@mcp.tool()
async def get_tp_grouped_by_em(ctx: Context) -> PLCQueryResult:
    """
    按EM描述分组获取TP控制逻辑

    Args:
        ctx: FastMCP上下文对象

    Returns:
        PLCQueryResult: 分组查询结果
    """
    await ctx.info("按EM分组查询TP控制逻辑")

    try:
        db = next(get_database())

        grouped_data = tp_control.get_grouped_by_em(db)

        await ctx.info(f"分组查询完成: {len(grouped_data)} 个分组")

        return PLCQueryResult(
            success=True,
            total=len(grouped_data),
            items=grouped_data,
            message=f"分组查询完成: {len(grouped_data)} 个分组"
        )

    except Exception as e:
        await ctx.error(f"分组查询失败: {e}")
        return PLCQueryResult(
            success=False,
            total=0,
            items=[],
            error=str(e)
        )
    finally:
        db.close()


# 修复model_dump调用
@mcp.tool()
async def get_tp_grouped_by_em_no_input(ctx: Context) -> PLCQueryResult:
                    name="query_tp_control",
                    description="查询TP控制逻辑数据",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "step_id": {
                                "type": "integer",
                                "description": "步骤ID筛选"
                            },
                            "em_description": {
                                "type": "string", 
                                "description": "EM描述筛选"
                            },
                            "em_variable": {
                                "type": "string",
                                "description": "EM变量筛选"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "返回记录数限制",
                                "default": 50
                            }
                        }
                    }
                ),
                Tool(
                    name="query_op_control", 
                    description="查询OP控制逻辑数据",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "step_id": {
                                "type": "integer",
                                "description": "步骤ID筛选"
                            },
                            "em_description": {
                                "type": "string",
                                "description": "EM描述筛选"
                            },
                            "em_variable": {
                                "type": "string", 
                                "description": "EM变量筛选"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "返回记录数限制",
                                "default": 50
                            }
                        }
                    }
                ),
                Tool(
                    name="query_actuator_map",
                    description="查询执行器变量映射数据",
                    inputSchema={
                        "type": "object", 
                        "properties": {
                            "command_description": {
                                "type": "string",
                                "description": "命令描述筛选"
                            },
                            "variable_name": {
                                "type": "string",
                                "description": "变量名筛选"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "返回记录数限制",
                                "default": 50
                            }
                        }
                    }
                ),
                Tool(
                    name="generate_tp_code",
                    description="生成TP代码",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "em_description": {
                                "type": "string",
                                "description": "EM描述筛选，用于生成特定EM的代码"
                            }
                        }
                    }
                ),
                Tool(
                    name="generate_op_code",
                    description="生成OP代码", 
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "em_description": {
                                "type": "string",
                                "description": "EM描述筛选，用于生成特定EM的代码"
                            }
                        }
                    }
                ),
                Tool(
                    name="validate_conditions",
                    description="验证条件映射完整性",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "detailed": {
                                "type": "boolean",
                                "description": "是否返回详细分析",
                                "default": False
                            }
                        }
                    }
                ),
                Tool(
                    name="test_condition_mapping",
                    description="测试单个条件的映射结果",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "condition_text": {
                                "type": "string",
                                "description": "要测试的条件文本",
                                "required": True
                            },
                            "step_data": {
                                "type": "object",
                                "description": "步骤数据上下文",
                                "properties": {
                                    "em_variable": {
                                        "type": "string",
                                        "default": "io_stEmBtryIn"
                                    },
                                    "op_sequence_under_em": {
                                        "type": "string", 
                                        "default": "OP[1]"
                                    }
                                }
                            }
                        },
                        "required": ["condition_text"]
                    }
                ),
                Tool(
                    name="get_tp_grouped_by_em",
                    description="按EM描述分组获取TP控制逻辑",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """处理工具调用"""
            
            # 获取数据库会话
            db = next(get_database())
            
            try:
                if name == "query_tp_control":
                    return await self._query_tp_control(db, arguments)
                elif name == "query_op_control":
                    return await self._query_op_control(db, arguments)
                elif name == "query_actuator_map":
                    return await self._query_actuator_map(db, arguments)
                elif name == "generate_tp_code":
                    return await self._generate_tp_code(db, arguments)
                elif name == "generate_op_code":
                    return await self._generate_op_code(db, arguments)
                elif name == "validate_conditions":
                    return await self._validate_conditions(db, arguments)
                elif name == "test_condition_mapping":
                    return await self._test_condition_mapping(db, arguments)
                elif name == "get_tp_grouped_by_em":
                    return await self._get_tp_grouped_by_em(db, arguments)
                else:
                    return [TextContent(type="text", text=f"未知工具: {name}")]
            
            except Exception as e:
                return [TextContent(type="text", text=f"工具执行错误: {str(e)}")]
            
            finally:
                db.close()
    
    async def _query_tp_control(self, db: Session, arguments: Dict[str, Any]) -> List[TextContent]:
        """查询TP控制逻辑"""
        filters = {}
        if arguments.get("step_id"):
            filters["step_id"] = str(arguments["step_id"])
        if arguments.get("em_description"):
            filters["em_description_cn"] = arguments["em_description"]
        if arguments.get("em_variable"):
            filters["em_variable"] = arguments["em_variable"]
        
        limit = arguments.get("limit", 50)
        
        items = tp_control.get_multi(db, skip=0, limit=limit, filters=filters)
        total = tp_control.count(db, filters=filters)
        
        result = {
            "total": total,
            "items": [
                {
                    "id": item.id,
                    "step_id": item.step_id,
                    "step_description": item.step_description,
                    "motion_condition": item.motion_condition,
                    "motion_action": item.motion_action,
                    "skip_condition": item.skip_condition,
                    "skip_step": item.skip_step,
                    "tp_sequence": item.tp_sequence,
                    "em_description_cn": item.em_description_cn,
                    "em_variable": item.em_variable,
                    "op_sequence_under_em": item.op_sequence_under_em
                }
                for item in items
            ]
        }
        
        return [TextContent(
            type="text", 
            text=f"TP控制逻辑查询结果:\n总数: {total}\n返回: {len(items)} 条记录\n\n" + 
                 json.dumps(result, ensure_ascii=False, indent=2)
        )]
    
    async def _query_op_control(self, db: Session, arguments: Dict[str, Any]) -> List[TextContent]:
        """查询OP控制逻辑"""
        filters = {}
        if arguments.get("step_id"):
            filters["step_id"] = str(arguments["step_id"])
        if arguments.get("em_description"):
            filters["em_description_cn"] = arguments["em_description"]
        if arguments.get("em_variable"):
            filters["em_variable"] = arguments["em_variable"]
        
        limit = arguments.get("limit", 50)
        
        items = op_control.get_multi(db, skip=0, limit=limit, filters=filters)
        total = op_control.count(db, filters=filters)
        
        result = {
            "total": total,
            "items": [
                {
                    "id": item.id,
                    "step_id": item.step_id,
                    "step_description": item.step_description,
                    "command_description_cn": item.command_description_cn,
                    "recovery_action_description_cn": item.recovery_action_description_cn,
                    "em_description_cn": item.em_description_cn,
                    "em_variable": item.em_variable,
                    "op_sequence_under_em": item.op_sequence_under_em
                }
                for item in items
            ]
        }
        
        return [TextContent(
            type="text",
            text=f"OP控制逻辑查询结果:\n总数: {total}\n返回: {len(items)} 条记录\n\n" +
                 json.dumps(result, ensure_ascii=False, indent=2)
        )]

    async def _query_actuator_map(self, db: Session, arguments: Dict[str, Any]) -> List[TextContent]:
        """查询执行器变量映射"""
        filters = {}
        if arguments.get("command_description"):
            filters["command_description_cn"] = arguments["command_description"]
        if arguments.get("variable_name"):
            filters["variable_name"] = arguments["variable_name"]

        limit = arguments.get("limit", 50)

        items = actuator_map.get_multi(db, skip=0, limit=limit, filters=filters)
        total = actuator_map.count(db, filters=filters)

        result = {
            "total": total,
            "items": [
                {
                    "id": item.id,
                    "command_description_cn": item.command_description_cn,
                    "variable_name": item.variable_name,
                    "parent_struct": item.parent_struct,
                    "actuator": item.actuator,
                    "op": item.op
                }
                for item in items
            ]
        }

        return [TextContent(
            type="text",
            text=f"执行器变量映射查询结果:\n总数: {total}\n返回: {len(items)} 条记录\n\n" +
                 json.dumps(result, ensure_ascii=False, indent=2)
        )]

    async def _generate_tp_code(self, db: Session, arguments: Dict[str, Any]) -> List[TextContent]:
        """生成TP代码"""
        em_description = arguments.get("em_description")

        try:
            code, warnings, unmapped = code_generator_service.generate_tp_code(
                db, em_description=em_description
            )

            result = {
                "code": code,
                "warnings": warnings,
                "unmapped_conditions": unmapped,
                "generated_at": datetime.now().isoformat()
            }

            return [TextContent(
                type="text",
                text=f"TP代码生成完成\n" +
                     f"EM描述: {em_description or '全部'}\n" +
                     f"警告数: {len(warnings)}\n" +
                     f"未映射条件数: {len(unmapped)}\n\n" +
                     f"生成的代码:\n```\n{code}\n```\n\n" +
                     (f"警告信息:\n{json.dumps(warnings, ensure_ascii=False, indent=2)}\n\n" if warnings else "") +
                     (f"未映射条件:\n{json.dumps(unmapped, ensure_ascii=False, indent=2)}" if unmapped else "")
            )]

        except Exception as e:
            return [TextContent(
                type="text",
                text=f"TP代码生成失败: {str(e)}"
            )]

    async def _generate_op_code(self, db: Session, arguments: Dict[str, Any]) -> List[TextContent]:
        """生成OP代码"""
        em_description = arguments.get("em_description")

        try:
            code, warnings, unmapped = code_generator_service.generate_op_code(
                db, em_description=em_description
            )

            result = {
                "code": code,
                "warnings": warnings,
                "unmapped_conditions": unmapped,
                "generated_at": datetime.now().isoformat()
            }

            return [TextContent(
                type="text",
                text=f"OP代码生成完成\n" +
                     f"EM描述: {em_description or '全部'}\n" +
                     f"警告数: {len(warnings)}\n" +
                     f"未映射条件数: {len(unmapped)}\n\n" +
                     f"生成的代码:\n```\n{code}\n```\n\n" +
                     (f"警告信息:\n{json.dumps(warnings, ensure_ascii=False, indent=2)}\n\n" if warnings else "") +
                     (f"未映射条件:\n{json.dumps(unmapped, ensure_ascii=False, indent=2)}" if unmapped else "")
            )]

        except Exception as e:
            return [TextContent(
                type="text",
                text=f"OP代码生成失败: {str(e)}"
            )]

    async def _validate_conditions(self, db: Session, arguments: Dict[str, Any]) -> List[TextContent]:
        """验证条件映射完整性"""
        detailed = arguments.get("detailed", False)

        try:
            validation_result = code_generator_service.validate_conditions(db)

            if detailed:
                detailed_analysis = code_generator_service.analyze_conditions_detailed(db)
                validation_result.update(detailed_analysis)

            return [TextContent(
                type="text",
                text=f"条件映射验证结果:\n\n" +
                     json.dumps(validation_result, ensure_ascii=False, indent=2)
            )]

        except Exception as e:
            return [TextContent(
                type="text",
                text=f"条件映射验证失败: {str(e)}"
            )]

    async def _test_condition_mapping(self, db: Session, arguments: Dict[str, Any]) -> List[TextContent]:
        """测试单个条件的映射结果"""
        condition_text = arguments.get("condition_text")
        step_data = arguments.get("step_data", {
            "em_variable": "io_stEmBtryIn",
            "op_sequence_under_em": "OP[1]"
        })

        if not condition_text:
            return [TextContent(
                type="text",
                text="错误: 缺少必需参数 condition_text"
            )]

        try:
            # 测试条件映射
            mapping_result = code_generator_service.find_condition_mapping(
                db, condition_text, step_data
            )

            # 获取映射建议
            suggestions = code_generator_service.get_condition_mapping_suggestions(
                db, condition_text
            )

            result = {
                "condition": condition_text,
                "step_data": step_data,
                "mapped_result": mapping_result,
                "suggestions": suggestions
            }

            return [TextContent(
                type="text",
                text=f"条件映射测试结果:\n\n" +
                     json.dumps(result, ensure_ascii=False, indent=2)
            )]

        except Exception as e:
            return [TextContent(
                type="text",
                text=f"条件映射测试失败: {str(e)}"
            )]

    async def _get_tp_grouped_by_em(self, db: Session, arguments: Dict[str, Any]) -> List[TextContent]:
        """按EM描述分组获取TP控制逻辑"""
        try:
            grouped_data = tp_control.get_grouped_by_em(db)

            return [TextContent(
                type="text",
                text=f"TP控制逻辑按EM分组结果:\n\n" +
                     json.dumps(grouped_data, ensure_ascii=False, indent=2)
            )]

        except Exception as e:
            return [TextContent(
                type="text",
                text=f"获取分组数据失败: {str(e)}"
            )]

    async def run_stdio(self):
        """运行stdio服务器"""
        async with stdio_server(
            StdioServerParameters(
                server=self.server,
                name="plc-auto-coding",
                version="1.0.0"
            )
        ) as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="plc-auto-coding",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities()
                )
            )
