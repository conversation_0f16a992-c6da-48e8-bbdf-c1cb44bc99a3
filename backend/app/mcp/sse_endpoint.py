"""
SSE (Server-Sent Events) 端点，用于提供MCP服务
"""

import json
import asyncio
from typing import Any, Dict, Optional, AsyncGenerator
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse
from sqlalchemy.orm import Session

from ..api.deps import get_database
from .server import PLCMCPServer


router = APIRouter()


class MCPSSEHandler:
    """MCP SSE处理器"""
    
    def __init__(self):
        self.mcp_server = PLCMCPServer()
        self.active_connections = set()
    
    async def handle_mcp_request(self, tool_name: str, arguments: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """处理MCP请求"""
        try:
            # 直接调用MCP服务器的工具处理方法
            if tool_name == "query_tp_control":
                result = await self.mcp_server._query_tp_control(db, arguments)
            elif tool_name == "query_op_control":
                result = await self.mcp_server._query_op_control(db, arguments)
            elif tool_name == "query_actuator_map":
                result = await self.mcp_server._query_actuator_map(db, arguments)
            elif tool_name == "generate_tp_code":
                result = await self.mcp_server._generate_tp_code(db, arguments)
            elif tool_name == "generate_op_code":
                result = await self.mcp_server._generate_op_code(db, arguments)
            elif tool_name == "validate_conditions":
                result = await self.mcp_server._validate_conditions(db, arguments)
            elif tool_name == "test_condition_mapping":
                result = await self.mcp_server._test_condition_mapping(db, arguments)
            elif tool_name == "get_tp_grouped_by_em":
                result = await self.mcp_server._get_tp_grouped_by_em(db, arguments)
            else:
                return {
                    "success": False,
                    "error": f"未知工具: {tool_name}",
                    "timestamp": datetime.now().isoformat()
                }
            
            # 提取文本内容
            text_content = result[0].text if result and len(result) > 0 else "无结果"
            
            return {
                "success": True,
                "tool_name": tool_name,
                "arguments": arguments,
                "result": text_content,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "tool_name": tool_name,
                "arguments": arguments,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def stream_mcp_response(self, tool_name: str, arguments: Dict[str, Any], db: Session) -> AsyncGenerator[str, None]:
        """流式返回MCP响应"""
        
        # 发送开始事件
        yield f"data: {json.dumps({'type': 'start', 'tool_name': tool_name, 'timestamp': datetime.now().isoformat()}, ensure_ascii=False)}\n\n"
        
        try:
            # 处理请求
            result = await self.handle_mcp_request(tool_name, arguments, db)
            
            # 发送结果事件
            yield f"data: {json.dumps({'type': 'result', **result}, ensure_ascii=False)}\n\n"
            
        except Exception as e:
            # 发送错误事件
            error_data = {
                "type": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
        
        finally:
            # 发送完成事件
            yield f"data: {json.dumps({'type': 'end', 'timestamp': datetime.now().isoformat()}, ensure_ascii=False)}\n\n"


# 创建全局处理器实例
mcp_handler = MCPSSEHandler()


@router.get("/mcp/tools")
async def list_mcp_tools():
    """列出所有可用的MCP工具"""
    tools = await mcp_handler.mcp_server.server.list_tools()()
    
    return {
        "success": True,
        "tools": [
            {
                "name": tool.name,
                "description": tool.description,
                "inputSchema": tool.inputSchema
            }
            for tool in tools
        ]
    }


@router.get("/mcp/stream/{tool_name}")
async def stream_mcp_tool(
    tool_name: str,
    request: Request,
    db: Session = Depends(get_database),
    # 通用查询参数
    step_id: Optional[int] = Query(None, description="步骤ID"),
    em_description: Optional[str] = Query(None, description="EM描述"),
    em_variable: Optional[str] = Query(None, description="EM变量"),
    command_description: Optional[str] = Query(None, description="命令描述"),
    variable_name: Optional[str] = Query(None, description="变量名"),
    condition_text: Optional[str] = Query(None, description="条件文本"),
    detailed: Optional[bool] = Query(False, description="详细分析"),
    limit: Optional[int] = Query(50, description="返回记录数限制")
):
    """通过SSE流式调用MCP工具"""
    
    # 构建参数
    arguments = {}
    if step_id is not None:
        arguments["step_id"] = step_id
    if em_description:
        arguments["em_description"] = em_description
    if em_variable:
        arguments["em_variable"] = em_variable
    if command_description:
        arguments["command_description"] = command_description
    if variable_name:
        arguments["variable_name"] = variable_name
    if condition_text:
        arguments["condition_text"] = condition_text
    if detailed is not None:
        arguments["detailed"] = detailed
    if limit:
        arguments["limit"] = limit
    
    # 添加step_data用于condition mapping测试
    if tool_name == "test_condition_mapping" and not arguments.get("step_data"):
        arguments["step_data"] = {
            "em_variable": em_variable or "io_stEmBtryIn",
            "op_sequence_under_em": "OP[1]"
        }
    
    async def event_generator():
        async for data in mcp_handler.stream_mcp_response(tool_name, arguments, db):
            yield data
    
    return EventSourceResponse(
        event_generator(),
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )


@router.post("/mcp/call/{tool_name}")
async def call_mcp_tool(
    tool_name: str,
    arguments: Dict[str, Any],
    db: Session = Depends(get_database)
):
    """直接调用MCP工具（非流式）"""
    
    result = await mcp_handler.handle_mcp_request(tool_name, arguments, db)
    return result


@router.get("/mcp/health")
async def mcp_health_check():
    """MCP服务健康检查"""
    return {
        "status": "healthy",
        "service": "mcp-server",
        "timestamp": datetime.now().isoformat(),
        "active_connections": len(mcp_handler.active_connections)
    }


@router.get("/mcp/info")
async def mcp_info():
    """获取MCP服务信息"""
    tools = await mcp_handler.mcp_server.server.list_tools()()
    
    return {
        "name": "PLC自动编码系统 MCP服务",
        "version": "1.0.0",
        "description": "提供PLC代码生成和数据查询的MCP服务",
        "tools_count": len(tools),
        "capabilities": [
            "查询TP控制逻辑",
            "查询OP控制逻辑", 
            "查询执行器变量映射",
            "生成TP代码",
            "生成OP代码",
            "验证条件映射",
            "测试条件映射",
            "按EM分组查询"
        ],
        "endpoints": {
            "list_tools": "/mcp/tools",
            "stream_tool": "/mcp/stream/{tool_name}",
            "call_tool": "/mcp/call/{tool_name}",
            "health": "/mcp/health",
            "info": "/mcp/info"
        }
    }
