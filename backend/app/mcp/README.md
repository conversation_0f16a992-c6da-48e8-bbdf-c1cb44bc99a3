# PLC自动编码系统 - MCP服务

本模块提供了基于MCP (Model Context Protocol) 的服务，支持通过SSE (Server-Sent Events) 方式进行实时查询和代码生成。

## 功能特性

### 🔍 数据查询功能
- **TP控制逻辑查询**: 支持按步骤ID、EM描述、EM变量等条件筛选
- **OP控制逻辑查询**: 支持多维度条件筛选和分页查询
- **执行器变量映射查询**: 查询命令描述与变量名的映射关系
- **按EM分组查询**: 获取按EM描述分组的TP控制逻辑数据

### 🛠️ 代码生成功能
- **TP代码生成**: 根据TP控制逻辑数据生成PLC代码
- **OP代码生成**: 根据OP控制逻辑数据生成PLC代码
- **实时流式输出**: 通过SSE提供实时的代码生成进度

### 🔧 辅助工具
- **条件映射验证**: 验证条件映射的完整性和正确性
- **条件映射测试**: 测试单个条件的映射结果和建议

## API端点

### 基础信息
- `GET /api/v1/mcp/info` - 获取MCP服务信息
- `GET /api/v1/mcp/health` - 健康检查
- `GET /api/v1/mcp/tools` - 列出所有可用工具

### SSE流式接口
- `GET /api/v1/mcp/stream/{tool_name}` - 通过SSE流式调用MCP工具

### 直接调用接口
- `POST /api/v1/mcp/call/{tool_name}` - 直接调用MCP工具（非流式）

## 可用工具

### 1. query_tp_control
查询TP控制逻辑数据

**参数:**
- `step_id` (可选): 步骤ID筛选
- `em_description` (可选): EM描述筛选
- `em_variable` (可选): EM变量筛选
- `limit` (可选): 返回记录数限制，默认50

### 2. query_op_control
查询OP控制逻辑数据

**参数:**
- `step_id` (可选): 步骤ID筛选
- `em_description` (可选): EM描述筛选
- `em_variable` (可选): EM变量筛选
- `limit` (可选): 返回记录数限制，默认50

### 3. query_actuator_map
查询执行器变量映射数据

**参数:**
- `command_description` (可选): 命令描述筛选
- `variable_name` (可选): 变量名筛选
- `limit` (可选): 返回记录数限制，默认50

### 4. generate_tp_code
生成TP代码

**参数:**
- `em_description` (可选): EM描述筛选，用于生成特定EM的代码

### 5. generate_op_code
生成OP代码

**参数:**
- `em_description` (可选): EM描述筛选，用于生成特定EM的代码

### 6. validate_conditions
验证条件映射完整性

**参数:**
- `detailed` (可选): 是否返回详细分析，默认false

### 7. test_condition_mapping
测试单个条件的映射结果

**参数:**
- `condition_text` (必需): 要测试的条件文本
- `step_data` (可选): 步骤数据上下文

### 8. get_tp_grouped_by_em
按EM描述分组获取TP控制逻辑

**参数:** 无

## 使用示例

### 1. 通过SSE流式查询TP数据
```bash
curl -N "http://localhost:8080/api/v1/mcp/stream/query_tp_control?em_description=电池入库&limit=10"
```

### 2. 生成TP代码
```bash
curl -N "http://localhost:8080/api/v1/mcp/stream/generate_tp_code?em_description=电池入库"
```

### 3. 测试条件映射
```bash
curl -N "http://localhost:8080/api/v1/mcp/stream/test_condition_mapping?condition_text=SV1在位置1&em_variable=io_stEmBtryIn"
```

### 4. 直接调用工具
```bash
curl -X POST "http://localhost:8080/api/v1/mcp/call/query_tp_control" \
  -H "Content-Type: application/json" \
  -d '{"em_description": "电池入库", "limit": 5}'
```

## 测试页面

访问 `http://localhost:8080/static/mcp_test.html` 可以使用Web界面测试MCP服务的各项功能。

## 独立MCP服务器

可以通过以下命令启动独立的MCP服务器：

```bash
python backend/app/mcp/standalone_server.py
```

这将启动一个通过stdio通信的MCP服务器，可以与支持MCP协议的客户端进行交互。

## 集成到大模型

MCP服务设计用于与大模型集成，提供以下能力：

1. **便捷查询**: 大模型可以通过自然语言查询PLC数据
2. **代码生成**: 自动生成PLC代码并提供实时反馈
3. **智能解释**: 利用大模型能力解释和优化生成的代码
4. **条件分析**: 智能分析条件映射的正确性和完整性

## 注意事项

1. SSE连接会在工具执行完成后自动关闭
2. 长时间运行的代码生成任务建议使用SSE流式接口
3. 所有接口都支持CORS，可以从前端直接调用
4. 错误信息会通过SSE事件流返回，便于实时处理
