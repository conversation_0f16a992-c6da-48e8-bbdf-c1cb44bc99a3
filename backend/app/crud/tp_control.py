"""
TP控制逻辑CRUD操作
"""

from typing import List, Optional, Dict
from sqlalchemy.orm import Session

from .base import CRUDBase
from ..models.tp_control import TPControlLogic
from ..schemas.tp_control import TPControlLogicCreate, TPControlLogicUpdate


class CRUDTPControlLogic(
    CRUDBase[TPControlLogic, TPControlLogicCreate, TPControlLogicUpdate]
):
    """TP控制逻辑CRUD操作类"""

    def get_by_step_id(self, db: Session, *, step_id: int) -> Optional[TPControlLogic]:
        """根据步骤ID获取记录"""
        return (
            db.query(TPControlLogic).filter(TPControlLogic.step_id == step_id).first()
        )

    def get_by_em_description(
        self, db: Session, *, em_description: str
    ) -> List[TPControlLogic]:
        """根据EM描述获取记录列表"""
        return (
            db.query(TPControlLogic)
            .filter(TPControlLogic.em_description_cn.like(f"%{em_description}%"))
            .order_by(TPControlLogic.step_id)
            .all()
        )

    def get_by_em_variable(
        self, db: Session, *, em_variable: str
    ) -> List[TPControlLogic]:
        """根据EM变量获取记录列表"""
        return (
            db.query(TPControlLogic)
            .filter(TPControlLogic.em_variable == em_variable)
            .order_by(TPControlLogic.step_id)
            .all()
        )

    def get_grouped_by_em(self, db: Session) -> dict:
        """按EM描述分组获取记录"""
        records = (
            db.query(TPControlLogic)
            .order_by(TPControlLogic.em_description_cn, TPControlLogic.step_id)
            .all()
        )

        grouped: Dict = {}
        for record in records:
            em_desc = record.em_description_cn or "未分类"
            if em_desc not in grouped:
                grouped[em_desc] = []
            grouped[em_desc].append(record)

        return grouped


# 创建CRUD实例
tp_control = CRUDTPControlLogic(TPControlLogic)
