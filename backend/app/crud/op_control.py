"""
OP控制逻辑CRUD操作
"""

from typing import List, Optional, Dict
from sqlalchemy.orm import Session

from .base import CRUDBase
from ..models.op_control import OPControlLogic
from ..schemas.op_control import OPControlLogicCreate, OPControlLogicUpdate


class CRUDOPControlLogic(
    CRUDBase[OPControlLogic, OPControlLogicCreate, OPControlLogicUpdate]
):
    """OP控制逻辑CRUD操作类"""

    def get_by_step_id(self, db: Session, *, step_id: int) -> Optional[OPControlLogic]:
        """根据步骤ID获取记录"""
        return (
            db.query(OPControlLogic).filter(OPControlLogic.step_id == step_id).first()
        )

    def get_by_em_description(
        self, db: Session, *, em_description: str
    ) -> List[OPControlLogic]:
        """根据EM描述获取记录列表"""
        return (
            db.query(OPControlLogic)
            .filter(OPControlLogic.em_description_cn.like(f"%{em_description}%"))
            .order_by(OPControlLogic.step_id)
            .all()
        )

    def get_by_em_variable(
        self, db: Session, *, em_variable: str
    ) -> List[OPControlLogic]:
        """根据EM变量获取记录列表"""
        return (
            db.query(OPControlLogic)
            .filter(OPControlLogic.em_variable == em_variable)
            .order_by(OPControlLogic.step_id)
            .all()
        )

    def get_grouped_by_em(self, db: Session) -> dict:
        """按EM描述分组获取记录"""
        records = (
            db.query(OPControlLogic)
            .order_by(OPControlLogic.em_description_cn, OPControlLogic.step_id)
            .all()
        )

        grouped: Dict = {}
        for record in records:
            em_desc = record.em_description_cn or "未分类"
            if em_desc not in grouped:
                grouped[em_desc] = []
            grouped[em_desc].append(record)

        return grouped


# 创建CRUD实例
op_control = CRUDOPControlLogic(OPControlLogic)
