"""
API依赖项
"""

from typing import Generator
from fastapi import HTTPException, status
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..schemas.common import PaginationParams


def get_pagination_params(page: int = 1, size: int = 20) -> PaginationParams:
    """获取分页参数"""
    if page < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="页码必须大于0"
        )
    if size < 1 or size > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="每页大小必须在1-100之间"
        )
    return PaginationParams(page=page, size=size)


def get_database() -> Generator[Session, None, None]:
    """获取数据库会话"""
    yield from get_db()
