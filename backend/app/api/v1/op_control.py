"""
OP控制逻辑API路由
"""

from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
import math

from ...api.deps import get_database, get_pagination_params
from ...crud.op_control import op_control
from ...schemas.op_control import (
    OPControlLogicCreate,
    OPControlLogicUpdate,
    OPControlLogicResponse,
    OPControlLogicListResponse,
)
from ...schemas.common import ResponseModel, PaginationParams

router = APIRouter()


@router.get("/", response_model=OPControlLogicListResponse)
def get_op_control_list(
    db: Session = Depends(get_database),
    pagination: PaginationParams = Depends(get_pagination_params),
    step_id: Optional[int] = Query(None, description="步骤ID筛选"),
    em_description: Optional[str] = Query(None, description="EM描述筛选"),
    em_variable: Optional[str] = Query(None, description="EM变量筛选"),
) -> Any:
    """获取OP控制逻辑列表"""

    # 构建筛选条件
    filters = {}
    if step_id is not None:
        filters["step_id"] = str(step_id)
    if em_description:
        filters["em_description_cn"] = em_description
    if em_variable:
        filters["em_variable"] = em_variable

    # 获取数据
    items = op_control.get_multi(
        db, skip=pagination.offset, limit=pagination.size, filters=filters
    )
    total = op_control.count(db, filters=filters)
    pages = math.ceil(total / pagination.size)

    return OPControlLogicListResponse(
        items=items,
        total=total,
        page=pagination.page,
        size=pagination.size,
        pages=pages,
    )


@router.get("/{op_id}", response_model=OPControlLogicResponse)
def get_op_control(op_id: int, db: Session = Depends(get_database)) -> Any:
    """获取单个OP控制逻辑记录"""

    op_record = op_control.get(db, id=op_id)
    if not op_record:
        raise HTTPException(status_code=404, detail="OP控制逻辑记录不存在")
    return op_record


@router.post("/", response_model=OPControlLogicResponse)
def create_op_control(
    op_in: OPControlLogicCreate, db: Session = Depends(get_database)
) -> Any:
    """创建OP控制逻辑记录"""

    # 检查步骤ID是否已存在
    existing = op_control.get_by_step_id(db, step_id=int(op_in.step_id))
    if existing:
        raise HTTPException(status_code=400, detail=f"步骤ID {op_in.step_id} 已存在")

    op_record = op_control.create(db, obj_in=op_in)
    return op_record


@router.put("/{op_id}", response_model=OPControlLogicResponse)
def update_op_control(
    op_id: int, op_in: OPControlLogicUpdate, db: Session = Depends(get_database)
) -> Any:
    """更新OP控制逻辑记录"""

    op_record = op_control.get(db, id=op_id)
    if not op_record:
        raise HTTPException(status_code=404, detail="OP控制逻辑记录不存在")

    # 如果更新步骤ID，检查是否与其他记录冲突
    if op_in.step_id is not None and op_in.step_id != op_record.step_id:
        existing = op_control.get_by_step_id(db, step_id=int(op_in.step_id))
        if existing and existing.id != op_id:
            raise HTTPException(
                status_code=400, detail=f"步骤ID {op_in.step_id} 已存在"
            )

    op_record = op_control.update(db, db_obj=op_record, obj_in=op_in)
    return op_record


@router.delete("/{op_id}", response_model=ResponseModel)
def delete_op_control(op_id: int, db: Session = Depends(get_database)) -> Any:
    """删除OP控制逻辑记录"""

    op_record = op_control.get(db, id=op_id)
    if not op_record:
        raise HTTPException(status_code=404, detail="OP控制逻辑记录不存在")

    op_control.remove(db, id=op_id)
    return ResponseModel(success=True, message="OP控制逻辑记录删除成功")


@router.get("/grouped/by-em", response_model=ResponseModel)
def get_op_control_grouped_by_em(db: Session = Depends(get_database)) -> Any:
    """按EM描述分组获取OP控制逻辑"""

    grouped_data = op_control.get_grouped_by_em(db)
    return ResponseModel(success=True, message="获取分组数据成功", data=grouped_data)
