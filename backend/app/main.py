"""
FastAPI主应用
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from sqlalchemy.exc import SQLAlchemyError
import os

from .core.config import settings
from .core.database import create_tables
from .api.v1.api import api_router

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="PLC自动编码系统 - 数据库表维护和代码生成服务",
    openapi_url="/api/v1/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.exception_handler(SQLAlchemyError)
async def sqlalchemy_exception_handler(request, exc):
    """处理SQLAlchemy异常"""
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "数据库操作失败",
            "error_code": "DATABASE_ERROR",
            "details": str(exc) if settings.debug else None,
        },
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """处理HTTP异常"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"success": False, "message": exc.detail, "error_code": "HTTP_ERROR"},
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """处理通用异常"""
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "error_code": "INTERNAL_ERROR",
            "details": str(exc) if settings.debug else None,
        },
    )


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    # 创建数据库表
    create_tables()
    print(f"🚀 {settings.app_name} v{settings.app_version} 启动成功!")
    print("📚 API文档: http://localhost:8080/docs")
    print(
        f"🔧 数据库: {settings.database_host}:{settings.database_port}/{settings.database_name}"
    )


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    print("👋 应用正在关闭...")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用 {settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs",
        "api": "/api/v1",
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
    }


# 注册API路由
app.include_router(api_router, prefix="/api/v1")

# 挂载静态文件
static_dir = os.path.join(os.path.dirname(__file__), "static")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=settings.debug)
