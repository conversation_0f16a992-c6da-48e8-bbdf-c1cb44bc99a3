<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP SSE 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .tools-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .tool-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .tool-name {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .tool-description {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>PLC自动编码系统 - MCP SSE 测试</h1>
    
    <div class="container">
        <h2>连接状态</h2>
        <div id="status" class="status disconnected">未连接</div>
    </div>

    <div class="container">
        <h2>可用工具</h2>
        <button onclick="loadTools()">加载工具列表</button>
        <div id="tools" class="tools-list"></div>
    </div>

    <div class="container">
        <h2>工具调用</h2>
        <div class="form-group">
            <label for="toolSelect">选择工具:</label>
            <select id="toolSelect">
                <option value="">请选择工具</option>
                <option value="query_tp_control">查询TP控制逻辑</option>
                <option value="query_op_control">查询OP控制逻辑</option>
                <option value="query_actuator_map">查询执行器变量映射</option>
                <option value="generate_tp_code">生成TP代码</option>
                <option value="generate_op_code">生成OP代码</option>
                <option value="validate_conditions">验证条件映射</option>
                <option value="test_condition_mapping">测试条件映射</option>
                <option value="get_tp_grouped_by_em">按EM分组查询TP</option>
            </select>
        </div>

        <div class="form-group">
            <label for="stepId">步骤ID:</label>
            <input type="number" id="stepId" placeholder="可选">
        </div>

        <div class="form-group">
            <label for="emDescription">EM描述:</label>
            <input type="text" id="emDescription" placeholder="可选">
        </div>

        <div class="form-group">
            <label for="emVariable">EM变量:</label>
            <input type="text" id="emVariable" placeholder="可选">
        </div>

        <div class="form-group">
            <label for="conditionText">条件文本 (用于测试条件映射):</label>
            <input type="text" id="conditionText" placeholder="例如: SV1在位置1">
        </div>

        <div class="form-group">
            <label for="detailed">详细分析:</label>
            <input type="checkbox" id="detailed">
        </div>

        <div class="form-group">
            <label for="limit">返回记录数限制:</label>
            <input type="number" id="limit" value="50" min="1" max="1000">
        </div>

        <button onclick="callTool()" id="callBtn">调用工具</button>
        <button onclick="clearOutput()">清空输出</button>
    </div>

    <div class="container">
        <h2>输出结果</h2>
        <div id="output" class="output">等待调用工具...</div>
    </div>

    <script>
        let eventSource = null;
        const statusEl = document.getElementById('status');
        const outputEl = document.getElementById('output');
        const callBtn = document.getElementById('callBtn');

        function updateStatus(status, message) {
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }

        async function loadTools() {
            try {
                const response = await fetch('/api/v1/mcp/tools');
                const data = await response.json();
                
                if (data.success) {
                    const toolsEl = document.getElementById('tools');
                    toolsEl.innerHTML = data.tools.map(tool => `
                        <div class="tool-card">
                            <div class="tool-name">${tool.name}</div>
                            <div class="tool-description">${tool.description}</div>
                        </div>
                    `).join('');
                } else {
                    alert('加载工具失败');
                }
            } catch (error) {
                alert('加载工具出错: ' + error.message);
            }
        }

        function buildQueryParams() {
            const params = new URLSearchParams();
            
            const stepId = document.getElementById('stepId').value;
            const emDescription = document.getElementById('emDescription').value;
            const emVariable = document.getElementById('emVariable').value;
            const conditionText = document.getElementById('conditionText').value;
            const detailed = document.getElementById('detailed').checked;
            const limit = document.getElementById('limit').value;

            if (stepId) params.append('step_id', stepId);
            if (emDescription) params.append('em_description', emDescription);
            if (emVariable) params.append('em_variable', emVariable);
            if (conditionText) params.append('condition_text', conditionText);
            if (detailed) params.append('detailed', 'true');
            if (limit) params.append('limit', limit);

            return params.toString();
        }

        function callTool() {
            const toolName = document.getElementById('toolSelect').value;
            if (!toolName) {
                alert('请选择工具');
                return;
            }

            if (eventSource) {
                eventSource.close();
            }

            const queryParams = buildQueryParams();
            const url = `/api/v1/mcp/stream/${toolName}${queryParams ? '?' + queryParams : ''}`;
            
            updateStatus('connecting', '连接中...');
            callBtn.disabled = true;
            outputEl.textContent = '开始调用工具...\n';

            eventSource = new EventSource(url);

            eventSource.onopen = function() {
                updateStatus('connected', '已连接');
            };

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    const timestamp = new Date(data.timestamp).toLocaleTimeString();
                    
                    switch (data.type) {
                        case 'start':
                            outputEl.textContent += `[${timestamp}] 开始执行工具: ${data.tool_name}\n`;
                            break;
                        case 'result':
                            outputEl.textContent += `[${timestamp}] 执行结果:\n${data.result}\n\n`;
                            break;
                        case 'error':
                            outputEl.textContent += `[${timestamp}] 错误: ${data.error}\n`;
                            break;
                        case 'end':
                            outputEl.textContent += `[${timestamp}] 执行完成\n`;
                            break;
                    }
                    
                    outputEl.scrollTop = outputEl.scrollHeight;
                } catch (error) {
                    outputEl.textContent += `解析响应出错: ${error.message}\n`;
                }
            };

            eventSource.onerror = function() {
                updateStatus('disconnected', '连接错误');
                callBtn.disabled = false;
                outputEl.textContent += '连接出错\n';
                eventSource.close();
            };

            eventSource.addEventListener('end', function() {
                updateStatus('disconnected', '已断开');
                callBtn.disabled = false;
                eventSource.close();
            });
        }

        function clearOutput() {
            outputEl.textContent = '等待调用工具...';
        }

        // 页面加载时自动加载工具列表
        window.onload = function() {
            loadTools();
        };
    </script>
</body>
</html>
